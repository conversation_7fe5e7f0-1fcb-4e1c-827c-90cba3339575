%load_ext autoreload
%autoreload 2

import pandas as pd
df = pd.read_pickle('data/df_ref_index.pkl')
print('{} datapoints'.format(len(df)))
df.head()

## Code to generate the features, might take a while
## if not using GPU acceleration. Consider using the
## pre-generated features in the data folder in that case.
df = df.head(10)
from mattervial.featurizers.structure import DescriptorMEGNetFeaturizer, MVLFeaturizer
# Default: ℓ-MM features
desc_l_mm = DescriptorMEGNetFeaturizer(base_descriptor='l-MM_v1')
features_l_mm = desc_l_mm.get_features(df['structure'])
print(features_l_mm.shape) # (:, 758) ℓ-MM features

# ℓ-OFM features
desc_ofm = DescriptorMEGNetFeaturizer(base_descriptor='l-OFM_v1')
features_l_ofm = desc_ofm.get_features(df['structure'])
print(features_l_ofm.shape) # (:, 188) ℓ-OFM features

# MVL features
mvl = MVLFeaturizer()
features_mvl = mvl.get_features(df['structure'])
print(features_mvl.shape) # (:, 240) MVL features (both layers)




# Save all dataframes to the ./data folder
features_l_mm.to_pickle('./data/ref_index_features_l_mm.pkl')
features_l_ofm.to_pickle('./data/ref_index_features_l_ofm.pkl')
features_mvl.to_pickle('./data/ref_index_features_mvl.pkl')

# print("Dataframes have been saved to the ./data folder.")

## Load the dataframes from the ./data folder
import pandas as pd

# Load the dataframes from the pickle files
df_mmv1 = pd.read_pickle('./data/df_mmv1.pkl')
df_ofm = pd.read_pickle('./data/df_ofm.pkl')
df_mvl32 = pd.read_pickle('./data/df_mvl32.pkl')
df_mvl16 = pd.read_pickle('./data/df_mvl16.pkl')

print("Dataframes have been loaded from the ./data folder.")

# The adjacent model needs to be trained beforehand.
# It is custom made for this dataset including the target.
from mattervial.featurizers.structure import ( adj_megnet,
                                        adj_megnet_layer16
                                       )
print("Training adj_megnet...")
# Consider reading the docs for the train_adjacent_megnet function
# before using it in your own projects.
adj_megnet.train_adjacent_megnet(df['structure'], df['ref_index'],
                                 adjacent_model_path='./data',
                                 max_epochs=10)

print("Featurizing with adj_megnet...")
df_adj_megnet = adj_megnet.get_features(df['structure'], model_path='./data')
print("adj_megnet features shape:", df_adj_megnet.shape)

print("Featurizing with adj_megnet_layer16...")
df_adj_megnet_layer16 = adj_megnet_layer16.get_features(df['structure'], model_path='./data')
print("adj_megnet_layer16 features shape:", df_adj_megnet_layer16.shape)

# Save dataframes to the ./data folder
df_adj_megnet.to_pickle('./data/df_adj_megnet.pkl')
df_adj_megnet_layer16.to_pickle('./data/df_adj_megnet_layer16.pkl')

print("Dataframes have been saved to the ./data folder.")

import pandas as pd

# Load the dataframes from the pickle files
df_adj_megnet = pd.read_pickle('./data/df_adj_megnet.pkl')
df_adj_megnet_layer16 = pd.read_pickle('./data/df_adj_megnet_layer16.pkl')

print("Dataframes have been loaded from the ./data folder.")

# We will now clean the features by removing the 'structure' column
def remove_structure_column(X):
    # Drop the 'structure' column if it exists
    if 'structure' in X.columns:
        X = X.drop(columns=['structure'])
    return X
# Removes 'structure' column from the features 
df_mmv1 = remove_structure_column(df_mmv1)
df_ofm = remove_structure_column(df_ofm)
df_mvl32 = remove_structure_column(df_mvl32)
df_mvl16 = remove_structure_column(df_mvl16)
df_adj_megnet = remove_structure_column(df_adj_megnet)
df_adj_megnet_layer16 = remove_structure_column(df_adj_megnet_layer16)

# Import necessary libraries
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from xgboost import XGBRegressor
import matplotlib.pyplot as plt
import seaborn as sns
    
# Ensure that the feature dataframes have the same index as df
df_mmv1.index = df.index
df_ofm.index = df.index
df_mvl32.index = df.index
df_mvl16.index = df.index
df_adj_megnet.index = df.index
df_adj_megnet_layer16.index = df.index

# Define combinations of features
feature_combinations = {
    'l_MM_v1': [df_mmv1],
    'l_MM_v1 + l_OFM_v1': [df_mmv1, df_ofm],
    'l_MM_v1 + l_OFM_v1 + mvl32': [df_mmv1, df_ofm, df_mvl32],
    'l_MM_v1 + l_OFM_v1 + mvl16': [df_mmv1, df_ofm, df_mvl16],
    'l_MM_v1 + l_OFM_v1 + mvl32 + adj_megnet': [df_mmv1, df_ofm, df_mvl32, df_adj_megnet],
    'l_MM_v1 + l_OFM_v1 + mvl16 + adj_megnet_layer16': [df_mmv1, df_ofm, df_mvl16, df_adj_megnet_layer16]
}

# Initialize a dataframe to store metrics
metrics_df = pd.DataFrame(columns=['Combination', 'MAE', 'RMSE', 'R2'])

# Loop over each combination
for combination_name, feature_dfs in feature_combinations.items():
    print(f'Processing combination: {combination_name}')
    # Concatenate features
    X = pd.concat(feature_dfs, axis=1)
    y = df['ref_index']
    
    # Split data into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Initialize and train the model
    model = XGBRegressor(objective='reg:squarederror', random_state=42)
    model.fit(X_train, y_train)
    
    # Predict on the test set
    y_pred = model.predict(X_test)
    
    # Calculate metrics
    mae = mean_absolute_error(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    r2 = r2_score(y_test, y_pred)
    
    # Store metrics
    metrics_df = metrics_df.append({
        'Combination': combination_name,
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2
    }, ignore_index=True)
    
    print(f'MAE: {mae:.4f}, RMSE: {rmse:.4f}, R2: {r2:.4f}')

# Extract data for plotting
combinations = metrics_df['Combination']
mae = metrics_df['MAE']
rmse = metrics_df['RMSE']
r2 = metrics_df['R2']

# Number of feature combinations
num_combinations = len(combinations)
x = np.arange(num_combinations)  # the label locations
width = 0.6  # width of the bars

# Create subplots
fig, axs = plt.subplots(3, 1, figsize=(14, 20))

# Function to add text annotations on top of bars
def add_text(ax, rects, metric_name):
    for rect in rects:
        height = rect.get_height()
        ax.annotate(f'{height:.4f}',
                    xy=(rect.get_x() + rect.get_width() / 2, height),
                    xytext=(0, 5),  # 5 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom',
                    fontsize=10,
                    fontweight='bold')

# Plot MAE
rects1 = axs[0].bar(x, mae, width, color='skyblue')
axs[0].set_ylabel('MAE', fontsize=12)
axs[0].set_title('Mean Absolute Error (MAE) by Feature Combination', fontsize=14)
axs[0].set_xticks(x)
axs[0].set_xticklabels(combinations, rotation=45, ha='right', fontsize=10)
axs[0].grid(axis='y', linestyle='--', alpha=0.7)
add_text(axs[0], rects1, 'MAE')
axs[0].set_ylim([0, max(mae)*1.2])

# Plot RMSE
rects2 = axs[1].bar(x, rmse, width, color='lightgreen')
axs[1].set_ylabel('RMSE', fontsize=12)
axs[1].set_title('Root Mean Squared Error (RMSE) by Feature Combination', fontsize=14)
axs[1].set_xticks(x)
axs[1].set_xticklabels(combinations, rotation=45, ha='right', fontsize=10)
axs[1].grid(axis='y', linestyle='--', alpha=0.7)
add_text(axs[1], rects2, 'RMSE')
axs[1].set_ylim([0, max(rmse)*1.2])

# Plot R²
rects3 = axs[2].bar(x, r2, width, color='salmon')
axs[2].set_ylabel('R²', fontsize=12)
axs[2].set_title('Coefficient of Determination (R²) by Feature Combination', fontsize=14)
axs[2].set_xticks(x)
axs[2].set_xticklabels(combinations, rotation=45, ha='right', fontsize=10)
axs[2].grid(axis='y', linestyle='--', alpha=0.7)
add_text(axs[2], rects3, 'R²')
axs[2].set_ylim([min(r2)*0.9, 1.0])  # R² typically ranges up to 1

# Adjust layout for better spacing
plt.tight_layout()

# Display the plots
plt.show()